<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fruit Ninja Game</title>
    <meta name="description" content="A fun and interactive fruit slicing game built with React and TypeScript" />
    <meta name="author" content="Fruit Ninja Game" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <meta property="og:title" content="Fruit Ninja Game" />
    <meta property="og:description" content="A fun and interactive fruit slicing game built with React and TypeScript" />
    <meta property="og:type" content="website" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Fruit Ninja Game" />
    <meta name="twitter:description" content="A fun and interactive fruit slicing game built with React and TypeScript" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
