import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FruitComponent } from './FruitComponent';
import { ScoreDisplay } from './ScoreDisplay';
import { GameOverScreen } from './GameOverScreen';
import { StartScreen } from './StartScreen';
import { ParticleEffect } from './ParticleEffect';

export interface Fruit {
  id: string;
  type: 'apple' | 'orange' | 'banana' | 'watermelon' | 'cherry';
  x: number;
  y: number;
  vx: number;
  vy: number;
  rotation: number;
  rotationSpeed: number;
  isSliced: boolean;
  size: number;
}

export interface SlashEffect {
  id: string;
  x: number;
  y: number;
  angle: number;
}

export interface Particle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  color: string;
  life: number;
}

export const FruitNinjaGame: React.FC = () => {
  const [gameState, setGameState] = useState<'start' | 'playing' | 'gameOver'>('start');
  const [score, setScore] = useState(0);
  const [combo, setCombo] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds game time
  const [fruits, setFruits] = useState<Fruit[]>([]);
  const [slashEffects, setSlashEffects] = useState<SlashEffect[]>([]);
  const [particles, setParticles] = useState<Particle[]>([]);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [isSlashing, setIsSlashing] = useState(false);
  const [swordTrail, setSwordTrail] = useState<Array<{x: number, y: number, id: string}>>([]);
  const gameRef = useRef<HTMLDivElement>(null);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const comboTimeout = useRef<NodeJS.Timeout | null>(null);

  const fruitTypes = ['apple', 'orange', 'banana', 'watermelon', 'cherry'] as const;
  const fruitColors = {
    apple: '#FF6B6B',
    orange: '#FF9F43',
    banana: '#FDD835',
    watermelon: '#26C6DA',
    cherry: '#EC407A'
  };

  const createFruit = useCallback((): Fruit => {
    const type = fruitTypes[Math.floor(Math.random() * fruitTypes.length)];
    const startX = Math.random() * window.innerWidth;
    const targetX = Math.random() * window.innerWidth;
    const vx = (targetX - startX) * 0.003;
    
    return {
      id: Math.random().toString(36).substr(2, 9),
      type,
      x: startX,
      y: window.innerHeight + 50,
      vx,
      vy: -15 - Math.random() * 10, // Increased initial velocity
      rotation: 0,
      rotationSpeed: (Math.random() - 0.5) * 12,
      isSliced: false,
      size: 80 + Math.random() * 40 // Larger fruits
    };
  }, []);

  const createParticles = useCallback((x: number, y: number, color: string) => {
    const newParticles: Particle[] = [];
    for (let i = 0; i < 15; i++) {
      newParticles.push({
        id: Math.random().toString(36).substr(2, 9),
        x,
        y,
        vx: (Math.random() - 0.5) * 25,
        vy: (Math.random() - 0.5) * 25,
        color,
        life: 1
      });
    }
    return newParticles;
  }, []);

  const sliceFruit = useCallback((fruitId: string, sliceX: number, sliceY: number) => {
    setFruits(prevFruits => {
      const fruit = prevFruits.find(f => f.id === fruitId);
      if (!fruit || fruit.isSliced) return prevFruits;

      // Create slash effect
      const angle = Math.atan2(
        mousePos.y - lastMousePos.current.y,
        mousePos.x - lastMousePos.current.x
      ) * 180 / Math.PI;

      setSlashEffects(prev => [...prev, {
        id: Math.random().toString(36).substr(2, 9),
        x: sliceX,
        y: sliceY,
        angle
      }]);

      // Create particles
      const newParticles = createParticles(sliceX, sliceY, fruitColors[fruit.type]);
      setParticles(prev => [...prev, ...newParticles]);

      // Update score and combo
      setScore(prev => prev + (10 * Math.max(1, combo)));
      setCombo(prev => prev + 1);

      // Reset combo timeout
      if (comboTimeout.current) {
        clearTimeout(comboTimeout.current);
      }
      comboTimeout.current = setTimeout(() => {
        setCombo(0);
      }, 2000);

      return prevFruits.map(f => 
        f.id === fruitId ? { ...f, isSliced: true } : f
      );
    });
  }, [mousePos, combo, createParticles]);

  const startGame = () => {
    setGameState('playing');
    setScore(0);
    setCombo(0);
    setTimeLeft(60);
    setFruits([]);
    setSlashEffects([]);
    setParticles([]);
    setSwordTrail([]);
  };

  const endGame = () => {
    setGameState('gameOver');
    setCombo(0);
    if (comboTimeout.current) {
      clearTimeout(comboTimeout.current);
    }
  };

  // Mouse/touch handling with sword trail
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const newPos = { x: e.clientX, y: e.clientY };
      lastMousePos.current = mousePos;
      setMousePos(newPos);
      
      if (isSlashing) {
        // Add to sword trail
        setSwordTrail(prev => {
          const newTrail = [...prev, { 
            x: newPos.x, 
            y: newPos.y, 
            id: Math.random().toString(36).substr(2, 9) 
          }];
          // Keep only last 8 positions for smooth trail
          return newTrail.slice(-8);
        });
      }
    };

    const handleMouseDown = () => {
      setIsSlashing(true);
      setSwordTrail([]);
    };

    const handleMouseUp = () => {
      setIsSlashing(false);
      setSwordTrail([]);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isSlashing, mousePos]);

  // Game timer
  useEffect(() => {
    if (gameState !== 'playing') return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          endGame();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [gameState]);

  // Game loop
  useEffect(() => {
    if (gameState !== 'playing') return;

    const gameLoop = setInterval(() => {
      // Spawn new fruits more frequently
      if (Math.random() < 0.08) { // Increased spawn rate
        setFruits(prev => [...prev, createFruit()]);
      }

      // Update fruits
      setFruits(prevFruits => {
        return prevFruits.map(fruit => ({
          ...fruit,
          x: fruit.x + fruit.vx,
          y: fruit.y + fruit.vy,
          vy: fruit.vy + 0.6, // gravity
          rotation: fruit.rotation + fruit.rotationSpeed
        })).filter(fruit => {
          // Remove fruits that are off-screen or sliced
          if (fruit.isSliced) return false;
          
          // Just remove fruits that fall off screen, no life penalty
          return fruit.y < window.innerHeight + 200 && 
                 fruit.x > -200 && 
                 fruit.x < window.innerWidth + 200;
        });
      });

      // Update particles
      setParticles(prevParticles => 
        prevParticles.map(particle => ({
          ...particle,
          x: particle.x + particle.vx,
          y: particle.y + particle.vy,
          vy: particle.vy + 0.4,
          life: particle.life - 0.025
        })).filter(particle => particle.life > 0)
      );

      // Remove old slash effects
      setSlashEffects(prev => prev.filter((_, index) => index < 5));
    }, 16); // ~60fps

    return () => clearInterval(gameLoop);
  }, [gameState, createFruit]);

  // Remove slash effects after animation
  useEffect(() => {
    if (slashEffects.length === 0) return;
    
    const timeout = setTimeout(() => {
      setSlashEffects(prev => prev.slice(1));
    }, 400);
    return () => clearTimeout(timeout);
  }, [slashEffects.length]);

  if (gameState === 'start') {
    return <StartScreen onStart={startGame} />;
  }

  if (gameState === 'gameOver') {
    return <GameOverScreen score={score} onRestart={startGame} />;
  }

  return (
    <div 
      ref={gameRef}
      className="game-container w-full h-screen relative cursor-none select-none"
    >
      <ScoreDisplay score={score} combo={combo} timeLeft={timeLeft} />
      
      {/* Fruits */}
      {fruits.map(fruit => (
        <FruitComponent
          key={fruit.id}
          fruit={fruit}
          onSlice={sliceFruit}
          isSlashing={isSlashing}
          mousePos={mousePos}
        />
      ))}

      {/* Slash Effects */}
      {slashEffects.map(slash => (
        <div
          key={slash.id}
          className="absolute w-40 h-3 bg-gradient-to-r from-transparent via-yellow-300 to-transparent animate-slash-effect pointer-events-none"
          style={{
            left: slash.x - 80,
            top: slash.y - 1.5,
            transform: `rotate(${slash.angle}deg)`,
            filter: 'blur(2px)',
            boxShadow: '0 0 30px rgba(255, 255, 0, 0.9)',
            zIndex: 1000
          }}
        />
      ))}

      {/* Particles */}
      {particles.map(particle => (
        <ParticleEffect key={particle.id} particle={particle} />
      ))}

      {/* Enhanced Sword Trail */}
      {isSlashing && swordTrail.map((pos, index) => {
        const opacity = (index + 1) / swordTrail.length;
        const scale = opacity;
        return (
          <div
            key={pos.id}
            className="absolute pointer-events-none"
            style={{
              left: pos.x - 3,
              top: pos.y - 25,
              width: '6px',
              height: '50px',
              background: `linear-gradient(to bottom, 
                transparent, 
                rgba(255, 255, 0, ${opacity * 0.8}), 
                rgba(255, 107, 53, ${opacity}), 
                transparent)`,
              borderRadius: '3px',
              transform: `scale(${scale}) rotate(${Math.atan2(
                index > 0 ? pos.y - swordTrail[index - 1].y : 0,
                index > 0 ? pos.x - swordTrail[index - 1].x : 0
              )}rad)`,
              transformOrigin: 'center',
              boxShadow: `0 0 15px rgba(255, 255, 0, ${opacity * 0.6})`,
              zIndex: 999
            }}
          />
        );
      })}

      {/* Combo indicator */}
      {combo > 1 && (
        <div
          className="fixed text-5xl font-bold text-yellow-300 animate-score-pop pointer-events-none z-50"
          style={{
            left: mousePos.x - 80,
            top: mousePos.y - 80,
            textShadow: '3px 3px 6px rgba(0, 0, 0, 0.8)',
            filter: 'drop-shadow(0 0 10px rgba(255, 255, 0, 0.8))'
          }}
        >
          {combo}x COMBO!
        </div>
      )}
    </div>
  );
};
