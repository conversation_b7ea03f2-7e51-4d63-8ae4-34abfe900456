
import React from 'react';
import { Timer } from 'lucide-react';

interface ScoreDisplayProps {
  score: number;
  combo: number;
  timeLeft: number;
}

export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ score, combo, timeLeft }) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    if (timeLeft <= 10) return 'text-red-400';
    if (timeLeft <= 30) return 'text-yellow-400';
    return 'text-green-400';
  };

  return (
    <div className="absolute top-0 left-0 right-0 z-50 p-6">
      <div className="flex justify-between items-start">
        {/* Score */}
        <div className="bg-black/50 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/20">
          <div className="text-white/70 text-sm font-medium mb-1">SCORE</div>
          <div className="text-3xl font-bold text-white score-text animate-bounce-in">
            {score.toLocaleString()}
          </div>
          {combo > 1 && (
            <div className="text-yellow-300 text-lg font-bold animate-pulse">
              {combo}x COMBO!
            </div>
          )}
        </div>

        {/* Timer */}
        <div className="bg-black/50 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/20">
          <div className="text-white/70 text-sm font-medium mb-1">TIME</div>
          <div className="flex items-center space-x-2">
            <Timer 
              className={`w-6 h-6 ${getTimeColor()}`}
              style={{
                filter: timeLeft <= 10 ? 'drop-shadow(0 0 8px rgba(239, 68, 68, 0.6))' : 'none'
              }}
            />
            <div className={`text-3xl font-bold ${getTimeColor()} score-text ${timeLeft <= 10 ? 'animate-pulse' : ''}`}>
              {formatTime(timeLeft)}
            </div>
          </div>
        </div>
      </div>

      {/* Game instructions */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4">
        <div className="bg-black/30 backdrop-blur-sm rounded-xl px-4 py-2 text-white/80 text-center">
          <div className="text-sm">Click and drag to slice fruits! 🥷</div>
        </div>
      </div>
    </div>
  );
};
