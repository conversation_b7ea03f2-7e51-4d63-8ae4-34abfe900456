
import React from 'react';
import { RotateCcw, Trophy, Star } from 'lucide-react';

interface GameOverScreenProps {
  score: number;
  onRestart: () => void;
}

export const GameOverScreen: React.FC<GameOverScreenProps> = ({ score, onRestart }) => {
  const getScoreRating = (score: number) => {
    if (score >= 1000) return { rating: 'LEGENDARY NINJA!', stars: 3, color: 'text-yellow-300' };
    if (score >= 500) return { rating: 'FRUIT MASTER!', stars: 2, color: 'text-orange-300' };
    if (score >= 200) return { rating: 'NICE SLICING!', stars: 1, color: 'text-green-300' };
    return { rating: 'KEEP PRACTICING!', stars: 0, color: 'text-blue-300' };
  };

  const { rating, stars, color } = getScoreRating(score);

  return (
    <div className="game-container w-full h-screen flex items-center justify-center">
      <div className="text-center animate-bounce-in">
        {/* Game Over Title */}
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-white mb-4 score-text">
            GAME OVER
          </h1>
          <div className="text-4xl mb-6">💀</div>
        </div>

        {/* Score Display */}
        <div className="bg-black/50 backdrop-blur-sm rounded-3xl p-8 mb-8 border border-white/20">
          <div className="text-white/70 text-lg font-medium mb-2">FINAL SCORE</div>
          <div className="text-6xl font-bold text-white score-text mb-4">
            {score.toLocaleString()}
          </div>
          
          {/* Rating */}
          <div className={`text-2xl font-bold ${color} mb-4`}>
            {rating}
          </div>
          
          {/* Stars */}
          <div className="flex justify-center space-x-2 mb-4">
            {Array.from({ length: 3 }, (_, index) => (
              <Star
                key={index}
                className={`w-8 h-8 transition-all duration-500 ${
                  index < stars
                    ? 'text-yellow-300 fill-yellow-300 animate-pulse'
                    : 'text-gray-600'
                }`}
                style={{
                  animationDelay: `${index * 0.2}s`
                }}
              />
            ))}
          </div>
        </div>

        {/* Performance breakdown */}
        <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-6 mb-8 text-white/80">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-ninja-orange">{Math.floor(score / 10)}</div>
              <div className="text-sm">Fruits Sliced</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-ninja-yellow">{Math.floor(score / 50)}</div>
              <div className="text-sm">Best Combo</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-ninja-green">{Math.floor(score / 100)}%</div>
              <div className="text-sm">Accuracy</div>
            </div>
          </div>
        </div>

        {/* Restart button */}
        <button
          onClick={onRestart}
          className="group relative bg-gradient-to-r from-ninja-green to-ninja-blue text-white font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl animate-pulse-glow mb-6"
        >
          <div className="flex items-center space-x-3">
            <RotateCcw className="w-6 h-6" />
            <span>PLAY AGAIN</span>
          </div>
          
          {/* Button glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-ninja-green to-ninja-blue rounded-full opacity-75 group-hover:opacity-100 transition-opacity blur-xl -z-10" />
        </button>

        {/* Motivational message */}
        <div className="text-white/60 text-lg max-w-md mx-auto">
          {score >= 500 ? (
            <p>🎉 Incredible! You're a true fruit ninja master!</p>
          ) : score >= 200 ? (
            <p>⚡ Great job! Your slicing skills are improving!</p>
          ) : (
            <p>🥷 Practice makes perfect! Try again to beat your score!</p>
          )}
        </div>

        {/* Trophy for high scores */}
        {score >= 1000 && (
          <div className="mt-6">
            <Trophy className="w-16 h-16 text-yellow-300 mx-auto animate-bounce" />
          </div>
        )}
      </div>

      {/* Falling fruit particles background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 10 }, (_, i) => (
          <div
            key={i}
            className="absolute text-4xl opacity-20 animate-fruit-fly"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${4 + Math.random() * 2}s`,
              '--random-x': `${(Math.random() - 0.5) * 200}px`
            } as React.CSSProperties}
          >
            {['🍎', '🍊', '🍌', '🍉', '🍒'][Math.floor(Math.random() * 5)]}
          </div>
        ))}
      </div>
    </div>
  );
};
