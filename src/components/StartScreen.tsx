
import React from 'react';
import { Sword, Play } from 'lucide-react';

interface StartScreenProps {
  onStart: () => void;
}

export const StartScreen: React.FC<StartScreenProps> = ({ onStart }) => {
  return (
    <div className="game-container w-full h-screen flex items-center justify-center">
      <div className="text-center animate-bounce-in">
        {/* Title */}
        <div className="mb-8">
          <h1 className="text-8xl font-bold text-white mb-4 score-text">
            FRUIT
            <span className="block text-ninja-orange">NINJA</span>
          </h1>
          <div className="flex justify-center mb-6">
            <Sword className="w-16 h-16 text-yellow-300 animate-pulse transform rotate-45" />
          </div>
        </div>

        {/* Animated fruits */}
        <div className="flex justify-center space-x-8 mb-12">
          {['🍎', '🍊', '🍌', '🍉', '🍒'].map((fruit, index) => (
            <div
              key={index}
              className="text-6xl animate-bounce"
              style={{
                animationDelay: `${index * 0.2}s`,
                animationDuration: '2s'
              }}
            >
              {fruit}
            </div>
          ))}
        </div>

        {/* Instructions */}
        <div className="mb-8 text-white/80 text-xl max-w-md mx-auto">
          <p className="mb-4">Slice fruits with your mouse!</p>
          <p className="text-lg">Don't let them fall or you'll lose a life! 💔</p>
        </div>

        {/* Start button */}
        <button
          onClick={onStart}
          className="group relative bg-gradient-to-r from-ninja-orange to-ninja-red text-white font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl animate-pulse-glow"
        >
          <div className="flex items-center space-x-3">
            <Play className="w-6 h-6 fill-white" />
            <span>START SLICING!</span>
          </div>
          
          {/* Button glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-ninja-orange to-ninja-red rounded-full opacity-75 group-hover:opacity-100 transition-opacity blur-xl -z-10" />
        </button>

        {/* Tips */}
        <div className="mt-8 text-white/60 text-sm">
          <p>💡 Tip: Create combos by slicing multiple fruits in one motion!</p>
        </div>
      </div>

      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full animate-ping"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};
