
import React, { useEffect, useState } from 'react';
import { Fruit } from './FruitNinjaGame';

interface FruitComponentProps {
  fruit: Fruit;
  onSlice: (fruitId: string, x: number, y: number) => void;
  isSlashing: boolean;
  mousePos: { x: number; y: number };
}

export const FruitComponent: React.FC<FruitComponentProps> = ({
  fruit,
  onSlice,
  isSlashing,
  mousePos
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const fruitEmojis = {
    apple: '🍎',
    orange: '🍊', 
    banana: '🍌',
    watermelon: '🍉',
    cherry: '🍒'
  };

  const fruitColors = {
    apple: '#FF6B6B',
    orange: '#FF9F43',
    banana: '#FDD835',
    watermelon: '#26C6DA',
    cherry: '#EC407A'
  };

  // Check for collision with mouse while slashing
  useEffect(() => {
    if (!isSlashing) return;

    const distance = Math.sqrt(
      Math.pow(mousePos.x - (fruit.x + fruit.size / 2), 2) +
      Math.pow(mousePos.y - (fruit.y + fruit.size / 2), 2)
    );

    if (distance < fruit.size / 2 + 20) {
      onSlice(fruit.id, fruit.x + fruit.size / 2, fruit.y + fruit.size / 2);
    }
  }, [mousePos, isSlashing, fruit, onSlice]);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  if (fruit.isSliced) {
    return null;
  }

  return (
    <div
      className={`absolute transition-all duration-75 fruit-shadow ${
        isHovered ? 'scale-110' : 'scale-100'
      }`}
      style={{
        left: fruit.x,
        top: fruit.y,
        width: fruit.size,
        height: fruit.size,
        transform: `rotate(${fruit.rotation}deg)`,
        zIndex: 10
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Fruit glow effect */}
      <div
        className="absolute inset-0 rounded-full opacity-20 animate-pulse-glow"
        style={{
          background: `radial-gradient(circle, ${fruitColors[fruit.type]}, transparent)`,
          filter: 'blur(8px)'
        }}
      />
      
      {/* Main fruit body */}
      <div
        className="relative w-full h-full rounded-full flex items-center justify-center text-4xl select-none animate-bounce-in"
        style={{
          background: `linear-gradient(135deg, ${fruitColors[fruit.type]}, ${fruitColors[fruit.type]}dd)`,
          border: `3px solid ${fruitColors[fruit.type]}`,
          boxShadow: `
            inset 0 -8px 16px rgba(0, 0, 0, 0.2),
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 2px rgba(255, 255, 255, 0.1)
          `,
          fontSize: `${fruit.size * 0.6}px`
        }}
      >
        {/* Fruit emoji/icon */}
        <span
          className="absolute filter drop-shadow-lg"
          style={{
            fontSize: `${fruit.size * 0.5}px`,
            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.3)'
          }}
        >
          {fruitEmojis[fruit.type]}
        </span>
        
        {/* Highlight effect */}
        <div
          className="absolute top-2 left-2 w-4 h-4 bg-white rounded-full opacity-60"
          style={{
            width: `${fruit.size * 0.15}px`,
            height: `${fruit.size * 0.15}px`,
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent)'
          }}
        />
      </div>

      {/* Juice splatter preview on hover */}
      {isHovered && (
        <div
          className="absolute inset-0 rounded-full animate-ping opacity-30"
          style={{
            background: `radial-gradient(circle, ${fruitColors[fruit.type]}, transparent)`,
            transform: 'scale(1.5)'
          }}
        />
      )}
    </div>
  );
};
