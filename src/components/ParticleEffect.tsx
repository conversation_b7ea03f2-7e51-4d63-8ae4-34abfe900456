
import React from 'react';
import { Particle } from './FruitNinjaGame';

interface ParticleEffectProps {
  particle: Particle;
}

export const ParticleEffect: React.FC<ParticleEffectProps> = ({ particle }) => {
  return (
    <div
      className="absolute w-2 h-2 rounded-full animate-particle-burst pointer-events-none"
      style={{
        left: particle.x,
        top: particle.y,
        backgroundColor: particle.color,
        opacity: particle.life,
        boxShadow: `0 0 6px ${particle.color}`,
        transform: `scale(${particle.life})`,
        zIndex: 100
      }}
    />
  );
};
