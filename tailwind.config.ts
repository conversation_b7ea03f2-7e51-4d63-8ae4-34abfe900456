
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// Fruit Ninja theme colors
				ninja: {
					orange: '#FF6B35',
					red: '#F7931E',
					yellow: '#FFD23F',
					green: '#4CAF50',
					purple: '#9C27B0',
					blue: '#2196F3'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fruit-fly': {
					'0%': {
						transform: 'translateY(100vh) translateX(0) rotate(0deg)',
						opacity: '1'
					},
					'100%': {
						transform: 'translateY(-100px) translateX(var(--random-x)) rotate(720deg)',
						opacity: '0'
					}
				},
				'fruit-slice': {
					'0%': {
						transform: 'scale(1) rotate(0deg)',
						opacity: '1'
					},
					'50%': {
						transform: 'scale(1.2) rotate(180deg)',
						opacity: '0.8'
					},
					'100%': {
						transform: 'scale(0) rotate(360deg)',
						opacity: '0'
					}
				},
				'slash-effect': {
					'0%': {
						opacity: '0',
						transform: 'scale(0.5)'
					},
					'50%': {
						opacity: '1',
						transform: 'scale(1.2)'
					},
					'100%': {
						opacity: '0',
						transform: 'scale(0.8)'
					}
				},
				'particle-burst': {
					'0%': {
						transform: 'scale(0) translateY(0)',
						opacity: '1'
					},
					'100%': {
						transform: 'scale(1) translateY(-50px)',
						opacity: '0'
					}
				},
				'score-pop': {
					'0%': {
						transform: 'scale(0.5) translateY(0)',
						opacity: '0'
					},
					'50%': {
						transform: 'scale(1.3) translateY(-20px)',
						opacity: '1'
					},
					'100%': {
						transform: 'scale(1) translateY(-40px)',
						opacity: '0'
					}
				},
				'bounce-in': {
					'0%': {
						transform: 'scale(0.3)',
						opacity: '0'
					},
					'50%': {
						transform: 'scale(1.05)',
						opacity: '0.8'
					},
					'70%': {
						transform: 'scale(0.9)',
						opacity: '1'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'pulse-glow': {
					'0%': {
						boxShadow: '0 0 0 0 rgba(255, 107, 53, 0.7)'
					},
					'70%': {
						boxShadow: '0 0 0 10px rgba(255, 107, 53, 0)'
					},
					'100%': {
						boxShadow: '0 0 0 0 rgba(255, 107, 53, 0)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fruit-fly': 'fruit-fly 3s linear forwards',
				'fruit-slice': 'fruit-slice 0.5s ease-out forwards',
				'slash-effect': 'slash-effect 0.3s ease-out forwards',
				'particle-burst': 'particle-burst 0.6s ease-out forwards',
				'score-pop': 'score-pop 1s ease-out forwards',
				'bounce-in': 'bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
				'pulse-glow': 'pulse-glow 2s infinite'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
