// Simple script to create a basic favicon
import fs from 'fs';

// Create a simple 16x16 red square ICO file (basic structure)
// This is a minimal ICO file with a red square
const icoData = Buffer.from([
  // ICO header
  0x00, 0x00, // Reserved
  0x01, 0x00, // Type (1 = ICO)
  0x01, 0x00, // Number of images
  
  // Image directory entry
  0x10, // Width (16)
  0x10, // Height (16)
  0x00, // Color count (0 = no palette)
  0x00, // Reserved
  0x01, 0x00, // Color planes
  0x20, 0x00, // Bits per pixel (32)
  0x00, 0x04, 0x00, 0x00, // Image size (1024 bytes)
  0x16, 0x00, 0x00, 0x00, // Image offset
  
  // BMP header
  0x28, 0x00, 0x00, 0x00, // Header size
  0x10, 0x00, 0x00, 0x00, // Width
  0x20, 0x00, 0x00, 0x00, // Height (doubled for ICO)
  0x01, 0x00, // Planes
  0x20, 0x00, // Bits per pixel
  0x00, 0x00, 0x00, 0x00, // Compression
  0x00, 0x04, 0x00, 0x00, // Image size
  0x00, 0x00, 0x00, 0x00, // X pixels per meter
  0x00, 0x00, 0x00, 0x00, // Y pixels per meter
  0x00, 0x00, 0x00, 0x00, // Colors used
  0x00, 0x00, 0x00, 0x00, // Important colors
]);

// Create pixel data (16x16 red square with transparency)
const pixelData = Buffer.alloc(1024); // 16x16x4 bytes (RGBA)

for (let i = 0; i < 256; i++) {
  const offset = i * 4;
  pixelData[offset] = 0x6B; // Blue component (BGR format)
  pixelData[offset + 1] = 0x6B; // Green component
  pixelData[offset + 2] = 0xFF; // Red component
  pixelData[offset + 3] = 0xFF; // Alpha component
}

// Combine header and pixel data
const fullIcoData = Buffer.concat([icoData, pixelData]);

// Write to file
fs.writeFileSync('public/favicon.ico', fullIcoData);
console.log('Favicon created successfully!');
